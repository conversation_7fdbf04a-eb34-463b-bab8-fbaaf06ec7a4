import { useUser } from "@clerk/clerk-react";
import { useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { useEffect } from "react";

export default function UserSaver() {
  const { user } = useUser();
  const saveUser = useMutation(api.users.saveUser);

  useEffect(() => {
    if (user) {
      const role = user.publicMetadata.userType as "user" | "designer" | "admin";
      if (role) {
        saveUser({
          clerkId: user.id,
          email: user.primaryEmailAddress!.emailAddress,
          role: role,
        });
      }
    }
  }, [user, saveUser]);

  return null;
}
