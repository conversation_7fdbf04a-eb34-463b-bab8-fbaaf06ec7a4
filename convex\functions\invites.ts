import { action } from "../_generated/server";
import { v } from "convex/values";
import type { ActionCtx } from "../_generated/server";

export const sendClerkInvite = action({
  args: v.object({ email: v.string() }),
  handler: async (_ctx: ActionCtx, { email }: { email: string }) => {
    if (!email) throw new Error("Email is required");

    const CLERK_SECRET_KEY = process.env.CLERK_SECRET_KEY;
    const RESEND_API_KEY = process.env.RESEND_API_KEY;

    if (!CLERK_SECRET_KEY) throw new Error("Clerk secret key is missing");
    if (!RESEND_API_KEY) throw new Error("Resend API key is missing");

    const redirectUrl = process.env.VITE_CLIENT_BASE_URL || "http://localhost:5176";

    // Step 1: Create the Clerk invitation
    const clerkResponse = await fetch("https://api.clerk.com/v1/invitations", {
      method: "POST",
      headers: {
        "Authorization": `Bearer ${CLERK_SECRET_KEY}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        email_address: email,
        redirect_url: `${redirectUrl}/designer-signup`,
        metadata: { role: "designer" },
      }),
    });

    if (!clerkResponse.ok) {
      const errText = await clerkResponse.text();
      throw new Error(`Clerk API error: ${clerkResponse.status} - ${errText}`);
    }

    const clerkData = await clerkResponse.json();
    console.log(`Clerk invitation created for ${email}:`, clerkData);

    // Step 2: Send the actual email using Resend
    const inviteUrl = clerkData.url; // This is the invitation URL from Clerk

    const emailHtml = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>You're invited to join TechShirt as a Designer</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
            <h1 style="color: white; margin: 0; font-size: 28px;">Welcome to TechShirt!</h1>
            <p style="color: white; margin: 10px 0 0 0; font-size: 16px;">You've been invited to join as a Designer</p>
          </div>

          <div style="background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px;">
            <p style="font-size: 16px; margin-bottom: 20px;">
              Hello! You've been invited to join <strong>TechShirt</strong> as a designer.
              TechShirt is the best place to design and sell custom tech-themed t-shirts.
            </p>

            <p style="font-size: 16px; margin-bottom: 25px;">
              As a designer, you'll be able to create amazing tech-themed designs and collaborate with our team.
            </p>

            <div style="text-align: center; margin: 30px 0;">
              <a href="${inviteUrl}"
                 style="background: #667eea; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; font-size: 16px; display: inline-block;">
                Accept Invitation
              </a>
            </div>

            <p style="font-size: 14px; color: #666; margin-top: 25px;">
              If the button doesn't work, you can copy and paste this link into your browser:
            </p>
            <p style="font-size: 14px; color: #667eea; word-break: break-all;">
              ${inviteUrl}
            </p>

            <hr style="border: none; border-top: 1px solid #ddd; margin: 25px 0;">

            <p style="font-size: 12px; color: #999; text-align: center;">
              This invitation was sent to ${email}. If you didn't expect this invitation, you can safely ignore this email.
            </p>
          </div>
        </body>
      </html>
    `;

    const emailResponse = await fetch("https://api.resend.com/emails", {
      method: "POST",
      headers: {
        "Authorization": `Bearer ${RESEND_API_KEY}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        from: "TechShirt <<EMAIL>>", // Using Resend's default testing domain
        to: [email],
        subject: "You're invited to join TechShirt as a Designer!",
        html: emailHtml,
      }),
    });

    if (!emailResponse.ok) {
      const emailError = await emailResponse.text();
      console.error(`Failed to send email: ${emailError}`);
      throw new Error(`Failed to send invitation email: ${emailResponse.status} - ${emailError}`);
    }

    const emailData = await emailResponse.json();
    console.log(`Invitation email sent to ${email}:`, emailData);

    return {
      clerkInvitation: clerkData,
      emailSent: emailData,
      inviteUrl: inviteUrl
    };
  },
});
