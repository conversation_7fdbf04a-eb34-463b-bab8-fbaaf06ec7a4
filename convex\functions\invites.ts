import { mutation } from "./_generated/server";
import { v } from "convex/values";
import type { MutationCtx } from "./_generated/server";

export const sendClerkInvite = mutation({
  args: v.object({ email: v.string() }),
  handler: async (_ctx: MutationCtx, { email }: { email: string }) => {
    if (!email) throw new Error("Email is required");

    const CLERK_SECRET_KEY = process.env.CLERK_SECRET_KEY;
    if (!CLERK_SECRET_KEY) throw new Error("Clerk secret key is missing");

    const redirectUrl = process.env.VITE_CLIENT_BASE_URL || "http://localhost:5176";

    const response = await fetch("https://api.clerk.com/v1/invitations", {
      method: "POST",
      headers: {
        "Authorization": `Bearer ${CLERK_SECRET_KEY}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        email_address: email,
        redirect_url: `${redirectUrl}/designer-signup`,
        metadata: { role: "designer" },
      }),
    });

    if (!response.ok) {
      const errText = await response.text();
      throw new Error(`Clerk API error: ${response.status} - ${errText}`);
    }

    const data = await response.json();
    console.log(`Clerk invitation sent to ${email}:`, data);

    return data;
  },
});
