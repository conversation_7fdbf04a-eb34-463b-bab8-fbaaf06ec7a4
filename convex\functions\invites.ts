import { action } from "../_generated/server";
import { v } from "convex/values";
import type { ActionCtx } from "../_generated/server";

export const sendClerkInvite = action({
  args: v.object({ email: v.string() }),
  handler: async (_ctx: ActionCtx, { email }: { email: string }) => {
    if (!email) throw new Error("Email is required");

    const CLERK_SECRET_KEY = process.env.CLERK_SECRET_KEY;

    if (!CLERK_SECRET_KEY) throw new Error("Clerk secret key is missing");

    const redirectUrl = process.env.VITE_CLIENT_BASE_URL || "http://localhost:5176";

    // Create the Clerk invitation
    const clerkResponse = await fetch("https://api.clerk.com/v1/invitations", {
      method: "POST",
      headers: {
        "Authorization": `Bearer ${CLERK_SECRET_KEY}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        email_address: email,
        redirect_url: `${redirectUrl}/signup/designer`,
        metadata: { role: "designer" },
      }),
    });

    if (!clerkResponse.ok) {
      const errText = await clerkResponse.text();

      // Handle duplicate invitation error - try to get existing invitation
      if (clerkResponse.status === 400 && errText.includes("duplicate invitation")) {
        console.log(`Duplicate invitation detected for ${email}, attempting to get existing invitation...`);

        // Try to get the existing invitation
        const listResponse = await fetch("https://api.clerk.com/v1/invitations", {
          method: "GET",
          headers: {
            "Authorization": `Bearer ${CLERK_SECRET_KEY}`,
            "Content-Type": "application/json",
          },
        });

        if (listResponse.ok) {
          const invitations = await listResponse.json();
          const existingInvitation = invitations.find((inv: any) =>
            inv.email_address === email && inv.status === "pending"
          );

          if (existingInvitation) {
            console.log(`Found existing invitation for ${email}`);
            const inviteUrl = existingInvitation.url;

            return {
              clerkInvitation: existingInvitation,
              emailSent: null,
              inviteUrl: inviteUrl,
              message: `Existing invitation found for ${email}. Share this link: ${inviteUrl}`
            };
          }
        }

        throw new Error(`An invitation has already been sent to ${email}. Please check if they received the previous invitation or try again later.`);
      }

      throw new Error(`Clerk API error: ${clerkResponse.status} - ${errText}`);
    }

    const clerkData = await clerkResponse.json();
    console.log(`Clerk invitation created for ${email}:`, clerkData);

    // Return the invitation URL for manual sharing
    const inviteUrl = clerkData.url;

    return {
      clerkInvitation: clerkData,
      emailSent: null,
      inviteUrl: inviteUrl,
      message: `Invitation created for ${email}! Share this link: ${inviteUrl}`
    };
  },
});
