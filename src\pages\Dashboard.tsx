import { useUser, useClerk, SignedIn } from "@clerk/clerk-react";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";

// Import your existing dashboard components
import AdminDashboardPage from './AdminDashboard';
import DesignerDashboardPage from './DesignerDashboard';
import ClientDashboardPage from './ClientDashboard';

export default function Dashboard() {
  const { isLoaded, isSignedIn, user } = useUser();
  const clerk = useClerk();
  const [userType, setUserType] = useState<string | null>(null);
  const navigate = useNavigate();

  useEffect(() => {
    if (!isLoaded) return;

    // Redirect to landing page if not signed in
    if (!isSignedIn) {
      navigate("/", { replace: true });
      return;
    }

    // Get userType safely from unsafeMetadata
    const type = typeof user?.unsafeMetadata?.userType === "string"
      ? user.unsafeMetadata.userType
      : null;

    setUserType(type);
  }, [isLoaded, isSignedIn, user, navigate]);

  // Logout function
  const handleLogout = async () => {
    await clerk.signOut();
    navigate("/", { replace: true });
  };

  if (!isLoaded) return <p>Loading...</p>;

  return (
    <SignedIn>
      <div className="flex flex-col items-center justify-center min-h-screen gap-6">
        <h1 className="text-3xl font-bold">
          {userType === "admin" && "Admin Dashboard"}
          {userType === "designer" && "Designer Dashboard"}
          {userType === "user" && "Client Dashboard"}
          {!userType && "Dashboard"}
        </h1>
        <p className="text-lg">
          Welcome {user?.firstName || "User"}! You are logged in as{" "}
          <span className="font-semibold">{userType || "Unknown"}</span>.
        </p>

        {/* Render the appropriate dashboard component */}
        {userType === "admin" && <AdminDashboardPage />}
        {userType === "designer" && <DesignerDashboardPage />}
        {userType === "user" && <ClientDashboardPage />}

        {/* Logout button */}
        <button
          type="button"
          onClick={handleLogout}
          className="mt-6 px-6 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition"
        >
          Logout
        </button>
      </div>
    </SignedIn>
  );
}
