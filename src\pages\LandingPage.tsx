import { Link } from 'react-router-dom';
import { SignedOut, SignedIn, SignInButton, useClerk, UserButton } from '@clerk/clerk-react';

export default function LandingPage() {
  const clerk = useClerk();

  const handleLogout = async () => {
    await clerk.signOut();
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-screen gap-4">
      <h1 className="text-3xl font-bold">Welcome to TechShirt</h1>
      <p className="text-lg text-center">
        The best place to design and buy custom tech-themed t-shirts.
      </p>

      {/* Show Sign Up / Sign In buttons if signed out */}
      <SignedOut>
        <div className="flex gap-4">
          <Link to="/sign-up">
            <button type="button" className="px-4 py-2 bg-blue-600 text-white rounded">Sign Up</button>
          </Link>

          <SignInButton>
            <button type="button" className="px-4 py-2 bg-gray-600 text-white rounded">Sign In</button>
          </SignInButton>
        </div>
      </SignedOut>

      {/* Show UserButton and Logout if signed in */}
      <SignedIn>
        <div className="flex gap-4 items-center">
          <UserButton />
          <button
            type="button"
            onClick={handleLogout}
            className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition"
          >
            Logout
          </button>
        </div>
      </SignedIn>
    </div>
  );
}
