import { useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";

export default function AdminDashboardPage() {
  const sendClerkInvite = useMutation(api.functions.invites.sendClerkInvite);

  const generateInvite = async () => {
    const email = prompt("Enter designer email:");
    if (!email) return;

    try {
      const result = await sendClerkInvite({ email });
      alert(`Invite sent to ${email}!`);
      console.log("Clerk invite result:", result);
    } catch (err) {
      console.error(err);
      alert("Failed to send invite. Check console for details.");
    }
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-screen gap-4">
      <h1 className="text-2xl font-bold">Admin Dashboard</h1>
      <button
        type="button"
        onClick={generateInvite}
        className="px-4 py-2 bg-blue-600 text-white rounded"
      >
        Invite Designer
      </button>
    </div>
  );
}
