import { useAction } from "convex/react";
import { api } from "../../convex/_generated/api";

export default function AdminDashboardPage() {
  const sendClerkInvite = useAction(api.functions.invites.sendClerkInvite);

  const generateInvite = async () => {
    const email = prompt("Enter designer email:");
    if (!email) return;

    try {
      const result = await sendClerkInvite({ email });

      // Show appropriate message based on whether email was sent or not
      if (result.emailSent) {
        alert(`✅ Invitation sent successfully to ${email}! They should receive an email shortly.`);
      } else {
        // Show the invitation URL for manual sharing
        const message = `✅ Invitation created for ${email}!\n\n` +
                       `Since you don't have a verified domain with Resend, please manually share this invitation link:\n\n` +
                       `${result.inviteUrl}\n\n` +
                       `Copy this link and send it to the designer via your preferred method (email, Slack, etc.)`;
        alert(message);
      }

      console.log("Clerk invite result:", result);
    } catch (err) {
      console.error(err);
      alert("Failed to send invite. Check console for details.");
    }
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-screen gap-4">
      <h1 className="text-2xl font-bold">Admin Dashboard</h1>
      <button
        type="button"
        onClick={generateInvite}
        className="px-4 py-2 bg-blue-600 text-white rounded"
      >
        Invite Designer
      </button>
    </div>
  );
}
