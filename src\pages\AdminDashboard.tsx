import { useAction } from "convex/react";
import { api } from "../../convex/_generated/api";

export default function AdminDashboardPage() {
  const sendClerkInvite = useAction(api.functions.invites.sendClerkInvite);

  const generateInvite = async () => {
    const email = prompt("Enter designer email:");
    if (!email) return;

    try {
      const result = await sendClerkInvite({ email });

      // Show the invitation URL for manual sharing
      const message = `✅ Invitation created for ${email}!\n\n` +
                     `Please share this invitation link with the designer:\n\n` +
                     `${result.inviteUrl}\n\n` +
                     `They can click this link to sign up as a designer. The link is secure and tied to their email address.`;

      alert(message);

      // Also copy to clipboard if possible
      if (navigator.clipboard) {
        navigator.clipboard.writeText(result.inviteUrl).then(() => {
          console.log("Invitation URL copied to clipboard!");
        }).catch(() => {
          console.log("Could not copy to clipboard, but URL is displayed above");
        });
      }

      console.log("Clerk invite result:", result);
    } catch (err) {
      console.error(err);
      alert("Failed to create invite. Check console for details.");
    }
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-screen gap-4">
      <h1 className="text-2xl font-bold">Admin Dashboard</h1>
      <button
        type="button"
        onClick={generateInvite}
        className="px-4 py-2 bg-blue-600 text-white rounded"
      >
        Invite Designer
      </button>
    </div>
  );
}
