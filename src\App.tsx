import { BrowserRouter, Routes, Route } from 'react-router-dom';
import SignUpPage from './pages/SignUpPage';
import SignInPage from './pages/SignInPage';
import DesignerSignUpPage from './pages/DesignerSignUpPage';
import LandingPage from './pages/LandingPage';
import Dashboard from './pages/Dashboard';
import AdminDashboardPage from './pages/AdminDashboard';
import DesignerDashboardPage from './pages/DesignerDashboard';
import ClientDashboardPage from './pages/ClientDashboard';



function App() {
  return (
    <BrowserRouter>
      <Routes>
        <Route path="/" element={<LandingPage />} />
        <Route path="/sign-up/*" element={<SignUpPage />} />
        <Route path="/sign-in/*" element={<SignInPage />} />
        <Route path="/signup/designer" element={<DesignerSignUpPage />} />

        <Route path="/dashboard" element={<Dashboard />} />
        <Route path="/admin" element={<AdminDashboardPage />} />
        <Route path="/designer" element={<DesignerDashboardPage />} />
        <Route path="/client" element={<ClientDashboardPage />} />
      </Routes>
    </BrowserRouter>
  );
}

export default App;
